import kotlinx.coroutines.runBlocking
import models.*
import org.junit.Test
import services.CybersourceSDKService
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class CybersourceSDKTest {
    
    private val cybersourceSDKService = CybersourceSDKService()
    
    @Test
    fun testSDKAuthorization() = runBlocking {
        // Create a test authorization request
        val request = PaymentAuthorizationRequest(
            paymentMethod = PaymentMethod(
                type = "credit-card"
            ),
            platform = Platform(
                source = "api"
            ),
            orderDetails = OrderDetails(
                amount = "10.00",
                currency = "USD",
                referenceCode = "TEST_SDK_AUTH_${System.currentTimeMillis()}"
            ),
            cardDetails = CardDetails(
                number = "****************",
                expirationMonth = "11",
                expirationYear = "2025",
                securityCode = "123"
            ),
            billingAddress = BillingAddress(
                firstName = "John",
                lastName = "Doe",
                address1 = "123 Test Street",
                city = "Foster City",
                state = "CA",
                postalCode = "94404",
                country = "US"
            ),
            customerInfo = CustomerInfo(
                email = "<EMAIL>",
                phoneNumber = "6504327113"
            )
        )
        
        try {
            val response = cybersourceSDKService.authorize(request)
            
            // Basic assertions
            assertNotNull(response)
            assertNotNull(response.status)
            
            println("SDK Authorization Test Result:")
            println("Success: ${response.success}")
            println("Status: ${response.status}")
            println("Payment ID: ${response.paymentId}")
            println("Reference Code: ${response.referenceCode}")
            
            if (response.error != null) {
                println("Error: ${response.error}")
            }
            
            if (response.transactionDetails != null) {
                println("Transaction Details: ${response.transactionDetails}")
            }
            
        } catch (e: Exception) {
            println("SDK Authorization test failed with exception: ${e.message}")
            e.printStackTrace()
        }
    }
    
    @Test
    fun testSDKCaptureAfterAuthorization() = runBlocking {
        // First, create an authorization
        val authRequest = PaymentAuthorizationRequest(
            paymentMethod = PaymentMethod(
                type = "credit-card"
            ),
            platform = Platform(
                source = "api"
            ),
            orderDetails = OrderDetails(
                amount = "25.50",
                currency = "USD",
                referenceCode = "TEST_SDK_CAPTURE_${System.currentTimeMillis()}"
            ),
            cardDetails = CardDetails(
                number = "****************",
                expirationMonth = "11",
                expirationYear = "2025",
                securityCode = "123"
            ),
            billingAddress = BillingAddress(
                firstName = "Jane",
                lastName = "Smith",
                address1 = "456 Test Avenue",
                city = "Foster City",
                state = "CA",
                postalCode = "94404",
                country = "US"
            ),
            customerInfo = CustomerInfo(
                email = "<EMAIL>",
                phoneNumber = "6504327113"
            )
        )
        
        try {
            val authResponse = cybersourceSDKService.authorize(authRequest)
            
            println("SDK Authorization for Capture Test:")
            println("Auth Success: ${authResponse.success}")
            println("Auth Payment ID: ${authResponse.paymentId}")
            
            if (authResponse.success && authResponse.paymentId != null) {
                // Now test capture
                val captureRequest = PaymentApprovalRequest(
                    paymentId = authResponse.paymentId!!,
                    orderDetails = OrderDetails(
                        amount = "25.50",
                        currency = "USD",
                        referenceCode = authRequest.orderDetails.referenceCode
                    ),
                    referenceCode = "CAPTURE_${authResponse.paymentId}"
                )
                
                val captureResponse = cybersourceSDKService.approve(captureRequest)
                
                println("SDK Capture Test Result:")
                println("Capture Success: ${captureResponse.success}")
                println("Capture Status: ${captureResponse.status}")
                println("Capture Payment ID: ${captureResponse.paymentId}")
                
                if (captureResponse.error != null) {
                    println("Capture Error: ${captureResponse.error}")
                }
                
            } else {
                println("Authorization failed, cannot test capture")
                if (authResponse.error != null) {
                    println("Auth Error: ${authResponse.error}")
                }
            }
            
        } catch (e: Exception) {
            println("SDK Capture test failed with exception: ${e.message}")
            e.printStackTrace()
        }
    }
}
