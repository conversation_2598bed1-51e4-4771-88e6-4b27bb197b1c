package routes

import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import models.*
import org.slf4j.LoggerFactory
import services.CybersourceService
import services.CybersourceSDKService

fun Route.paymentRoutes() {
    val cybersourceService by lazy {
        CybersourceService(
            httpClient = HttpClient(CIO),
            logger = LoggerFactory.getLogger(CybersourceService::class.java)
        )
    }

    val cybersourceSDKService by lazy {
        CybersourceSDKService()
    }

    route("/api/v1") {
        route("/payments") {

            // New dynamic payment initialization endpoint
            post("/init") {
                val logger = LoggerFactory.getLogger("PaymentRoutes.Init")

                try {
                    val request = call.receive<PaymentInitRequest>()

                    // Validate the initialization request
                    validateInitRequest(request)

                    logger.info("Processing payment init request for reference: ${request.orderDetails.referenceCode}")
                    logger.info("Payment method: ${request.paymentMethod.type}, Platform: ${request.platform.source}")

                    when (request.paymentMethod.type) {
                        "credit-card" -> {
                            // Convert to authorization request and process
                            val authRequest = PaymentAuthorizationRequest(
                                paymentMethod = request.paymentMethod,
                                platform = request.platform,
                                orderDetails = request.orderDetails,
                                cardDetails = request.cardDetails ?: throw IllegalArgumentException("Card details are required for credit card payments"),
                                billingAddress = request.billingAddress,
                                customerInfo = request.customerInfo
                            )

                            val response = cybersourceService.authorize(authRequest)
                            call.respond(response.toPaymentInitResponse())
                        }
                        "paypal" -> {
                            // Placeholder for future PayPal implementation
                            call.respond(
                                HttpStatusCode.NotImplemented,
                                PaymentInitResponse(
                                    success = false,
                                    status = PaymentStatus.FAILED,
                                    error = ErrorInfo(
                                        code = "NOT_IMPLEMENTED",
                                        message = "PayPal payment method is not yet implemented"
                                    )
                                )
                            )
                        }
                        "apple-pay" -> {
                            // Placeholder for future Apple Pay implementation
                            call.respond(
                                HttpStatusCode.NotImplemented,
                                PaymentInitResponse(
                                    success = false,
                                    status = PaymentStatus.FAILED,
                                    error = ErrorInfo(
                                        code = "NOT_IMPLEMENTED",
                                        message = "Apple Pay payment method is not yet implemented"
                                    )
                                )
                            )
                        }
                        "google-pay" -> {
                            // Placeholder for future Google Pay implementation
                            call.respond(
                                HttpStatusCode.NotImplemented,
                                PaymentInitResponse(
                                    success = false,
                                    status = PaymentStatus.FAILED,
                                    error = ErrorInfo(
                                        code = "NOT_IMPLEMENTED",
                                        message = "Google Pay payment method is not yet implemented"
                                    )
                                )
                            )
                        }
                        else -> {
                            call.respond(
                                HttpStatusCode.BadRequest,
                                PaymentInitResponse(
                                    success = false,
                                    status = PaymentStatus.FAILED,
                                    error = ErrorInfo(
                                        code = "UNSUPPORTED_PAYMENT_METHOD",
                                        message = "Unsupported payment method: ${request.paymentMethod.type}"
                                    )
                                )
                            )
                        }
                    }
                } catch (e: ContentTransformationException) {
                    logger.error("Invalid payment init request format", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentInitResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "INVALID_REQUEST_FORMAT",
                                message = "Invalid request format. Please check your JSON structure and required fields."
                            )
                        )
                    )
                } catch (e: IllegalArgumentException) {
                    logger.error("Validation error in payment init request", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentInitResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "VALIDATION_ERROR",
                                message = e.message ?: "Request validation failed"
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.error("Unexpected error processing payment init", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = PaymentInitResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }

            // Approve endpoint (formerly capture) - structured input
            post("/approve") {
                val logger = LoggerFactory.getLogger("PaymentRoutes.Approve")

                try {
                    val request = call.receive<PaymentApprovalRequest>()

                    // Validate structured request
                    validateApprovalRequest(request)

                    logger.info("Processing approval request for payment ID: ${request.paymentId}")

                    val response = cybersourceService.approve(request)

                    if (response.success) {
                        logger.info("Approval successful for payment ID: ${request.paymentId}")
                        call.respond(HttpStatusCode.OK, response)
                    } else {
                        logger.warn("Approval failed for payment ID: ${request.paymentId}. Reason: ${response.error?.message}")
                        call.respond(HttpStatusCode.BadRequest, response)
                    }

                } catch (e: ContentTransformationException) {
                    logger.error("Invalid approval request format", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "INVALID_REQUEST_FORMAT",
                                message = "Invalid request format. Please check your JSON structure and required fields."
                            )
                        )
                    )
                } catch (e: IllegalArgumentException) {
                    logger.error("Validation error in approval request", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "VALIDATION_ERROR",
                                message = e.message ?: "Request validation failed"
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.error("Unexpected error processing approval", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }

            post("/reverse") {
                val logger = LoggerFactory.getLogger("PaymentRoutes.Reversal")

                val reversalRequest = call.receive<ReversalRequest>()
                val paymentId = reversalRequest.reversalInformation.paymentId

                if (paymentId.isBlank()) {
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            error = ErrorInfo(
                                code = "MISSING_PAYMENT_ID",
                                message = "Payment ID is required"
                            )
                        )
                    )
                    return@post
                }

                try {
                    val response = cybersourceService.reversePayment(reversalRequest)
                    call.respond(HttpStatusCode.OK, response)
                } catch (e: Exception) {
                    logger.error("Reversal failed for ID $paymentId", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = PaymentResponse(
                            success = false,
                            status = PaymentStatus.FAILED,
                            paymentId = paymentId,
                            error = ErrorInfo(
                                code = "REVERSAL_FAILED",
                                message = "Failed to reverse payment"
                            )
                        )
                    )
                }
            }

            // SDK-based endpoints for testing
            route("/sdk") {

                // SDK Authorization endpoint
                post("/authorize") {
                    val logger = LoggerFactory.getLogger("PaymentRoutes.SDK.Authorize")

                    try {
                        val request = call.receive<PaymentAuthorizationRequest>()
                        validateAuthorizationRequest(request)

                        logger.info("Processing SDK authorization request for reference: ${request.orderDetails.referenceCode}")

                        val response = cybersourceSDKService.authorize(request)

                        if (response.success) {
                            logger.info("SDK Authorization successful for reference: ${request.orderDetails.referenceCode}")
                            call.respond(HttpStatusCode.OK, response)
                        } else {
                            logger.warn("SDK Authorization failed for reference: ${request.orderDetails.referenceCode}. Reason: ${response.error?.message}")
                            call.respond(HttpStatusCode.BadRequest, response)
                        }

                    } catch (e: ContentTransformationException) {
                        logger.error("Invalid SDK authorization request format", e)
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = PaymentResponse(
                                success = false,
                                status = PaymentStatus.FAILED,
                                error = ErrorInfo(
                                    code = "INVALID_REQUEST_FORMAT",
                                    message = "Invalid request format. Please check your JSON structure and required fields."
                                )
                            )
                        )
                    } catch (e: IllegalArgumentException) {
                        logger.error("Validation error in SDK authorization request", e)
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = PaymentResponse(
                                success = false,
                                status = PaymentStatus.FAILED,
                                error = ErrorInfo(
                                    code = "VALIDATION_ERROR",
                                    message = e.message ?: "Request validation failed"
                                )
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("Unexpected error in SDK authorization", e)
                        call.respond(
                            status = HttpStatusCode.InternalServerError,
                            message = PaymentResponse(
                                success = false,
                                status = PaymentStatus.FAILED,
                                error = ErrorInfo(
                                    code = "PROCESSING_ERROR",
                                    message = "An unexpected error occurred while processing your request"
                                )
                            )
                        )
                    }
                }

                // SDK Capture endpoint
                post("/capture") {
                    val logger = LoggerFactory.getLogger("PaymentRoutes.SDK.Capture")

                    try {
                        val request = call.receive<PaymentApprovalRequest>()
                        validateApprovalRequest(request)

                        logger.info("Processing SDK capture request for payment ID: ${request.paymentId}")

                        val response = cybersourceSDKService.approve(request)

                        if (response.success) {
                            logger.info("SDK Capture successful for payment ID: ${request.paymentId}")
                            call.respond(HttpStatusCode.OK, response)
                        } else {
                            logger.warn("SDK Capture failed for payment ID: ${request.paymentId}. Reason: ${response.error?.message}")
                            call.respond(HttpStatusCode.BadRequest, response)
                        }

                    } catch (e: Exception) {
                        logger.error("Error in SDK capture", e)
                        call.respond(
                            status = HttpStatusCode.InternalServerError,
                            message = PaymentResponse(
                                success = false,
                                status = PaymentStatus.FAILED,
                                error = ErrorInfo(
                                    code = "CAPTURE_ERROR",
                                    message = "Failed to process capture: ${e.message}"
                                )
                            )
                        )
                    }
                }

                // SDK Reversal endpoint
                post("/reverse") {
                    val logger = LoggerFactory.getLogger("PaymentRoutes.SDK.Reverse")

                    try {
                        val request = call.receive<ReversalRequest>()

                        logger.info("Processing SDK reversal request for payment ID: ${request.reversalInformation.paymentId}")

                        val response = cybersourceSDKService.reversePayment(request)

                        if (response.success) {
                            logger.info("SDK Reversal successful for payment ID: ${request.reversalInformation.paymentId}")
                            call.respond(HttpStatusCode.OK, response)
                        } else {
                            logger.warn("SDK Reversal failed for payment ID: ${request.reversalInformation.paymentId}. Reason: ${response.error?.message}")
                            call.respond(HttpStatusCode.BadRequest, response)
                        }

                    } catch (e: Exception) {
                        logger.error("Error in SDK reversal", e)
                        call.respond(
                            status = HttpStatusCode.InternalServerError,
                            message = PaymentResponse(
                                success = false,
                                status = PaymentStatus.FAILED,
                                error = ErrorInfo(
                                    code = "REVERSAL_ERROR",
                                    message = "Failed to process reversal: ${e.message}"
                                )
                            )
                        )
                    }
                }
            }

            // Test endpoint (bypassing SDK issues)
            route("/test") {

                // Test Authorization endpoint
                post("/authorize") {
                    val logger = LoggerFactory.getLogger("PaymentRoutes.TEST.Authorize")

                    try {
                        val request = call.receive<PaymentAuthorizationRequest>()
                        validateAuthorizationRequest(request)

                        logger.info("Processing TEST authorization request for reference: ${request.orderDetails.referenceCode}")

                        // Return a mock successful response to demonstrate the issue is with the SDK
                        val response = PaymentResponse(
                            success = true,
                            status = PaymentStatus.AUTHORIZED,
                            paymentId = "TEST_PAYMENT_${System.currentTimeMillis()}",
                            referenceCode = request.orderDetails.referenceCode,
                            transactionDetails = TransactionDetails(
                                amount = request.orderDetails.amount,
                                currency = request.orderDetails.currency,
                                transactionId = "TEST_TXN_${System.currentTimeMillis()}"
                            )
                        )

                        logger.info("TEST Authorization successful for reference: ${request.orderDetails.referenceCode}")
                        call.respond(HttpStatusCode.OK, response)

                    } catch (e: ContentTransformationException) {
                        logger.error("Invalid TEST authorization request format", e)
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = PaymentResponse(
                                success = false,
                                status = PaymentStatus.FAILED,
                                error = ErrorInfo(
                                    code = "INVALID_REQUEST_FORMAT",
                                    message = "Invalid request format. Please check your JSON structure and required fields."
                                )
                            )
                        )
                    } catch (e: IllegalArgumentException) {
                        logger.error("Validation error in TEST authorization request", e)
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = PaymentResponse(
                                success = false,
                                status = PaymentStatus.FAILED,
                                error = ErrorInfo(
                                    code = "VALIDATION_ERROR",
                                    message = e.message ?: "Validation failed"
                                )
                            )
                        )
                    } catch (e: Exception) {
                        logger.error("Error in TEST authorization", e)
                        call.respond(
                            status = HttpStatusCode.InternalServerError,
                            message = PaymentResponse(
                                success = false,
                                status = PaymentStatus.FAILED,
                                error = ErrorInfo(
                                    code = "AUTHORIZATION_ERROR",
                                    message = "Failed to process authorization: ${e.message}"
                                )
                            )
                        )
                    }
                }
            }

        }
    }
}


// Extension functions and validation functions moved outside the paymentRoutes() function

private fun PaymentResponse.toPaymentInitResponse(): PaymentInitResponse {
    return PaymentInitResponse(
        success = this.success,
        paymentId = this.paymentId,
        status = this.status,
        referenceCode = this.referenceCode,
        transactionDetails = this.transactionDetails,
        approvalInfo = this.approvalInfo,
        error = this.error,
        metadata = this.metadata
    )
}

private fun validateInitRequest(request: PaymentInitRequest) {
    // Validate payment method
    require(request.paymentMethod.type.isNotBlank()) { "Payment method type is required" }
    require(request.paymentMethod.type in listOf("credit-card", "paypal", "apple-pay", "google-pay")) {
        "Invalid payment method type. Supported types: credit-card, paypal, apple-pay, google-pay"
    }

    // Validate platform
    require(request.platform.source.isNotBlank()) { "Platform source is required" }
    require(request.platform.source in listOf("web", "mobile-app", "api")) {
        "Invalid platform source. Supported sources: web, mobile-app, api"
    }

    // Validate order details
    require(request.orderDetails.amount.isNotBlank()) { "Order amount is required" }
    require(request.orderDetails.currency.isNotBlank()) { "Order currency is required" }

    // Validate card details if paymentN method is credit-card
    if (request.paymentMethod.type == "credit-card") {
        require(request.cardDetails != null) { "Card details are required for credit card payments" }
        validateCardDetails(request.cardDetails)
    }

    // Validate billing address
    require(request.billingAddress.firstName.isNotBlank()) { "Billing first name is required" }
    require(request.billingAddress.lastName.isNotBlank()) { "Billing last name is required" }
    require(request.billingAddress.address1.isNotBlank()) { "Billing address is required" }
    require(request.billingAddress.city.isNotBlank()) { "Billing city is required" }
    require(request.billingAddress.state.isNotBlank()) { "Billing state is required" }
    require(request.billingAddress.postalCode.isNotBlank()) { "Billing postal code is required" }
    require(request.billingAddress.country.isNotBlank()) { "Billing country is required" }

    // Validate customer info
    require(request.customerInfo.email.isNotBlank()) { "Customer email is required" }
    require(request.customerInfo.phoneNumber.isNotBlank()) { "Customer phone number is required" }

    // Validate amount format
    require(request.orderDetails.amount.matches(Regex("\\d+\\.\\d{2}"))) {
        "Invalid amount format. Use decimal format like 102.21"
    }

    // Validate currency format
    require(request.orderDetails.currency.matches(Regex("[A-Z]{3}"))) {
        "Invalid currency format. Use 3-letter currency code like USD, EUR, etc."
    }

    // Validate email format
    require(request.customerInfo.email.contains("@") && request.customerInfo.email.contains(".")) {
        "Invalid email format"
    }
}

private fun validateCardDetails(cardDetails: CardDetails) {
    require(cardDetails.number.isNotBlank()) { "Card number is required" }
    require(cardDetails.expirationMonth.isNotBlank()) { "Card expiration month is required" }
    require(cardDetails.expirationYear.isNotBlank()) { "Card expiration year is required" }

    // Validate card number format
    require(cardDetails.number.replace(" ", "").matches(Regex("\\d{13,19}"))) {
        "Invalid card number format"
    }

    // Validate expiration month
    require(cardDetails.expirationMonth.matches(Regex("(0[1-9]|1[0-2])"))) {
        "Invalid expiration month. Use MM format (01-12)"
    }

    // Validate expiration year
    require(cardDetails.expirationYear.matches(Regex("\\d{4}")) &&
            cardDetails.expirationYear.toInt() >= 2024) {
        "Invalid expiration year. Use YYYY format and ensure it's not expired"
    }
}

private fun validateAuthorizationRequest(request: PaymentAuthorizationRequest) {
    // Validate payment method
    require(request.paymentMethod.type.isNotBlank()) { "Payment method type is required" }
    require(request.paymentMethod.type in listOf("credit-card", "debit-card", "digital-wallet")) {
        "Invalid payment method type. Supported types: credit-card, debit-card, digital-wallet"
    }

    // Validate platform
    require(request.platform.source.isNotBlank()) { "Platform source is required" }
    require(request.platform.source in listOf("web", "mobile-app", "api")) {
        "Invalid platform source. Supported sources: web, mobile-app, api"
    }

    // Validate order details
    require(request.orderDetails.amount.isNotBlank()) { "Order amount is required" }
    require(request.orderDetails.currency.isNotBlank()) { "Order currency is required" }

    // Validate card details (only for card payments)
    if (request.paymentMethod.type in listOf("credit-card", "debit-card")) {
        require(request.cardDetails.number.isNotBlank()) { "Card number is required for card payments" }
        require(request.cardDetails.expirationMonth.isNotBlank()) { "Card expiration month is required" }
        require(request.cardDetails.expirationYear.isNotBlank()) { "Card expiration year is required" }

        // Validate card number format
        require(request.cardDetails.number.replace(" ", "").matches(Regex("\\d{13,19}"))) {
            "Invalid card number format"
        }

        // Validate expiration month
        require(request.cardDetails.expirationMonth.matches(Regex("(0[1-9]|1[0-2])"))) {
            "Invalid expiration month. Use MM format (01-12)"
        }

        // Validate expiration year
        require(request.cardDetails.expirationYear.matches(Regex("\\d{4}")) &&
                request.cardDetails.expirationYear.toInt() >= 2024) {
            "Invalid expiration year. Use YYYY format and ensure it's not expired"
        }
    }

    // Validate billing address
    require(request.billingAddress.firstName.isNotBlank()) { "Billing first name is required" }
    require(request.billingAddress.lastName.isNotBlank()) { "Billing last name is required" }
    require(request.billingAddress.address1.isNotBlank()) { "Billing address is required" }
    require(request.billingAddress.city.isNotBlank()) { "Billing city is required" }
    require(request.billingAddress.state.isNotBlank()) { "Billing state is required" }
    require(request.billingAddress.postalCode.isNotBlank()) { "Billing postal code is required" }
    require(request.billingAddress.country.isNotBlank()) { "Billing country is required" }

    // Validate customer info
    require(request.customerInfo.email.isNotBlank()) { "Customer email is required" }
    require(request.customerInfo.phoneNumber.isNotBlank()) { "Customer phone number is required" }

    // Validate amount format
    require(request.orderDetails.amount.matches(Regex("\\d+\\.\\d{2}"))) {
        "Invalid amount format. Use decimal format like 102.21"
    }

    // Validate currency format
    require(request.orderDetails.currency.matches(Regex("[A-Z]{3}"))) {
        "Invalid currency format. Use 3-letter currency code like USD, EUR, etc."
    }

    // Validate email format
    require(request.customerInfo.email.contains("@") && request.customerInfo.email.contains(".")) {
        "Invalid email format"
    }
}

private fun validateApprovalRequest(request: PaymentApprovalRequest) {
    require(request.paymentId.isNotBlank()) { "Payment ID is required" }
    require(request.orderDetails.amount.isNotBlank()) { "Amount is required" }
    require(request.orderDetails.currency.isNotBlank()) { "Currency is required" }

    // Validate amount format
    require(request.orderDetails.amount.matches(Regex("\\d+\\.\\d{2}"))) {
        "Invalid amount format. Use decimal format like 102.21"
    }

    // Validate currency
    require(request.orderDetails.currency.matches(Regex("[A-Z]{3}"))) {
        "Invalid currency format. Use 3-letter currency code like USD, EUR, etc."
    }
}