package routes

import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.*
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import models.*
import org.slf4j.LoggerFactory
import services.UserService

fun Route.userRoutes() {
    val userService by lazy {
        UserService(
            httpClient = HttpClient(CIO),
            logger = LoggerFactory.getLogger(UserService::class.java)
        )
    }

    route("/api/v1") {
        route("/users") {

            // Create user endpoint
            post {
                val logger = LoggerFactory.getLogger("UserRoutes.Create")

                try {
                    val request = call.receive<UserCreateRequest>()

                    validateCreateRequest(request)

                    logger.info("Processing user creation request for customerId: ${request.buyerInfo.customerId}")
                    logger.info("Email: ${request.buyerInfo.email}")

                    val response = userService.createUser(request)

                    if (response.success) {
                        logger.info("User creation successful. User ID: ${response.userId}")
                        call.respond(HttpStatusCode.Created, response)
                    } else {
                        logger.warn("User creation failed. Reason: ${response.error?.message}")
                        call.respond(HttpStatusCode.BadRequest, response)
                    }

                } catch (e: ContentTransformationException) {
                    logger.error("Invalid user creation request format", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "INVALID_REQUEST_FORMAT",
                                message = "Invalid request format. Expected: {\"buyerInfo\": {\"customerId\": \"string\", \"email\": \"string\"}, \"referenceInfo\": {\"code\": \"string\"}, \"customData\": [{\"name\": \"string\", \"value\": \"string\"}]}"
                            )
                        )
                    )
                } catch (e: IllegalArgumentException) {
                    logger.error("Validation error in user creation request", e)
                    call.respond(
                        status = HttpStatusCode.BadRequest,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "VALIDATION_ERROR",
                                message = e.message ?: "Request validation failed"
                            )
                        )
                    )
                } catch (e: Exception) {
                    logger.error("Unexpected error processing user creation", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }

            post("/retrieve") {
                val logger = LoggerFactory.getLogger("UserRoutes.Retrieve")

                try {
                    val request = call.receive<UserRetrieveRequest>()

                    if (request.userId.isBlank()) {
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = UserResponse(
                                success = false,
                                error = ErrorInfo(
                                    code = "MISSING_USER_ID",
                                    message = "User ID is required"
                                )
                            )
                        )
                        return@post
                    }

                    logger.info("Processing user retrieval request for ID: ${request.userId}")

                    val response = userService.getUser(request.userId)

                    if (response.success) {
                        logger.info("User retrieval successful for ID: ${request.userId}")
                        call.respond(HttpStatusCode.OK, response)
                    } else {
                        logger.warn("User retrieval failed for ID: ${request.userId}. Reason: ${response.error?.message}")
                        val statusCode = if (response.error?.code == "RESOURCE_NOT_FOUND") {
                            HttpStatusCode.NotFound
                        } else {
                            HttpStatusCode.BadRequest
                        }
                        call.respond(statusCode, response)
                    }

                } catch (e: Exception) {
                    logger.error("Unexpected error processing user retrieval", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }

            // Get user endpoint (keeping original for backward compatibility)
            get("/{userId}") {
                val logger = LoggerFactory.getLogger("UserRoutes.Get")

                try {
                    val userId = call.parameters["userId"]

                    if (userId.isNullOrBlank()) {
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = UserResponse(
                                success = false,
                                error = ErrorInfo(
                                    code = "MISSING_USER_ID",
                                    message = "User ID is required"
                                )
                            )
                        )
                        return@get
                    }

                    logger.info("Processing user retrieval request for ID: $userId")

                    val response = userService.getUser(userId)

                    if (response.success) {
                        logger.info("User retrieval successful for ID: $userId")
                        call.respond(HttpStatusCode.OK, response)
                    } else {
                        logger.warn("User retrieval failed for ID: $userId. Reason: ${response.error?.message}")
                        // Return 404 if user not found, otherwise 400
                        val statusCode = if (response.error?.code == "RESOURCE_NOT_FOUND") {
                            HttpStatusCode.NotFound
                        } else {
                            HttpStatusCode.BadRequest
                        }
                        call.respond(statusCode, response)
                    }

                } catch (e: Exception) {
                    logger.error("Unexpected error processing user retrieval", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }

            post("/delete") {
                val logger = LoggerFactory.getLogger("UserRoutes.Delete")

                try {
                    val request = call.receive<UserDeleteRequest>()

                    if (request.userId.isBlank()) {
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = UserResponse(
                                success = false,
                                error = ErrorInfo(
                                    code = "MISSING_USER_ID",
                                    message = "User ID is required"
                                )
                            )
                        )
                        return@post
                    }

                    logger.info("Processing user deletion request for ID: ${request.userId}")

                    val response = userService.deleteUser(request.userId)

                    if (response.success) {
                        logger.info("User deletion successful for ID: ${request.userId}")
                        call.respond(HttpStatusCode.OK, response)
                    } else {
                        logger.warn("User deletion failed for ID: ${request.userId}. Reason: ${response.error?.message}")
                        val statusCode = if (response.error?.code == "RESOURCE_NOT_FOUND") {
                            HttpStatusCode.NotFound
                        } else {
                            HttpStatusCode.BadRequest
                        }
                        call.respond(statusCode, response)
                    }

                } catch (e: Exception) {
                    logger.error("Unexpected error processing user deletion", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }

            // Delete user endpoint (keeping original for backward compatibility)
            delete("/{userId}") {
                val logger = LoggerFactory.getLogger("UserRoutes.Delete")

                try {
                    val userId = call.parameters["userId"]

                    if (userId.isNullOrBlank()) {
                        call.respond(
                            status = HttpStatusCode.BadRequest,
                            message = UserResponse(
                                success = false,
                                error = ErrorInfo(
                                    code = "MISSING_USER_ID",
                                    message = "User ID is required"
                                )
                            )
                        )
                        return@delete
                    }

                    logger.info("Processing user deletion request for ID: $userId")

                    val response = userService.deleteUser(userId)

                    if (response.success) {
                        logger.info("User deletion successful for ID: $userId")
                        call.respond(HttpStatusCode.OK, response)
                    } else {
                        logger.warn("User deletion failed for ID: $userId. Reason: ${response.error?.message}")
                        // Return 404 if user not found, otherwise 400
                        val statusCode = if (response.error?.code == "RESOURCE_NOT_FOUND") {
                            HttpStatusCode.NotFound
                        } else {
                            HttpStatusCode.BadRequest
                        }
                        call.respond(statusCode, response)
                    }

                } catch (e: Exception) {
                    logger.error("Unexpected error processing user deletion", e)
                    call.respond(
                        status = HttpStatusCode.InternalServerError,
                        message = UserResponse(
                            success = false,
                            error = ErrorInfo(
                                code = "PROCESSING_ERROR",
                                message = "An unexpected error occurred while processing your request"
                            )
                        )
                    )
                }
            }
        }
    }
}

private fun validateCreateRequest(request: UserCreateRequest) {
    require(request.buyerInfo.customerId.isNotBlank()) { "Customer ID is required" }
    require(request.buyerInfo.email.isNotBlank()) { "Email is required" }
    require(request.referenceInfo.code.isNotBlank()) { "Reference code is required" }

    // Validate email format
    require(request.buyerInfo.email.contains("@") && request.buyerInfo.email.contains(".")) {
        "Invalid email format"
    }
}