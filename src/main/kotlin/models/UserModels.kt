package models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

// ============================================
// SIMPLIFIED USER API REQUEST MODELS
// ============================================

@Serializable
data class UserCreateRequest(
    val buyerInfo: BuyerInfo,
    val referenceInfo: ReferenceInfo,
    val customData: List<CustomData>? = null
)

@Serializable
data class BuyerInfo(
    val customerId: String,
    val email: String
)

@Serializable
data class ReferenceInfo(
    val code: String
)

@Serializable
data class CustomData(
    val name: String,
    val value: String
)

@Serializable
data class UserRetrieveRequest(
    val userId: String
)

@Serializable
data class UserDeleteRequest(
    val userId: String
)

// ============================================
// SIMPLIFIED USER RESPONSE MODELS
// ============================================

@Serializable
data class UserResponse(
    val success: Boolean,
    val userId: String? = null,
    val user: SimpleUserData? = null,
    val error: ErrorInfo? = null
)

@Serializable
data class SimpleUserData(
    val id: String,
    val customerId: String,
    val email: String,
    val referenceCode: String? = null,
    val customData: List<CustomData>? = null,
    val createdAt: String? = null
)

// ============================================
// CYBERSOURCE INTERNAL MODELS (for API calls)
// ============================================

@Serializable
data class CyberSourceUserCreateRequest(
    val buyerInformation: BuyerInformation,
    val clientReferenceInformation: ClientReferenceInformation,
    val merchantDefinedInformation: List<MerchantDefinedInformation>? = null
)

@Serializable
data class BuyerInformation(
    val merchantCustomerID: String,
    val email: String
)


@Serializable
data class MerchantDefinedInformation(
    val name: String,
    val value: String
)

@Serializable
data class CyberSourceUserResponse(
    @SerialName("_links") val links: Map<String, Link>? = null,
    val id: String? = null,
    val buyerInformation: BuyerInformation? = null,
    val clientReferenceInformation: ClientReferenceInformation? = null,
    val merchantDefinedInformation: List<MerchantDefinedInformation>? = null,
    val errorInformation: ErrorInformation? = null,
    val customerTokenId: String? = null,
    val instrumentIdentifier: InstrumentIdentifier? = null,
    val metadata: Map<String, String>? = null
)



@Serializable
data class InstrumentIdentifier(
    val id: String? = null
)
