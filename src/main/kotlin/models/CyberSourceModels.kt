package models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

// ============================================
// STRUCTURED API REQUEST MODELS
// ============================================

@Serializable
data class PaymentAuthorizationRequest(
    val paymentMethod: PaymentMethod,
    val platform: Platform,
    val orderDetails: OrderDetails,
    val cardDetails: CardDetails,
    val billingAddress: BillingAddress,
    val customerInfo: CustomerInfo
)

@Serializable
data class PaymentApprovalRequest(
    val paymentId: String,
    val orderDetails: OrderDetails,
    val referenceCode: String? = null
)

@Serializable
data class ReversalRequest(
   // val clientReferenceInformation: ClientReferenceInformation,
    val reversalInformation: ReversalInformation
)

@Serializable
data class PaymentInitRequest(
    val paymentMethod: PaymentMethod,
    val platform: Platform,
    val orderDetails: OrderDetails,
    val cardDetails: CardDetails? = null, // Optional for non-card payments
    val billingAddress: BillingAddress,
    val customerInfo: CustomerInfo
)

// New response model for payment initialization
@Serializable
data class PaymentInitResponse(
    val success: Boolean,
    val paymentId: String? = null,
    val status: PaymentStatus,
    val referenceCode: String? = null,
    val transactionDetails: TransactionDetails? = null,
    val approvalInfo: ApprovalInfo? = null,
    val error: ErrorInfo? = null,
    val metadata: PaymentMetadata? = null,
    val additionalData: Map<String, String>? = null // For payment method specific data
)

// ============================================
// STRUCTURED COMPONENTS
// ============================================

@Serializable
data class PaymentMethod(
    val type: String, // "credit-card", "debit-card", "digital-wallet"
)

@Serializable
data class Platform(
    val source: String, // "web", "mobile-app", "api"
    val userAgent: String? = null
)

@Serializable
data class OrderDetails(
    val referenceCode: String? = null,
    val amount: String,
    val currency: String,
    val description: String? = null,
    val merchantCategoryCode: String? = null
)

@Serializable
data class CardDetails(
    val number: String,
    val expirationMonth: String,
    val expirationYear: String,
    val securityCode: String? = null,
    val cardholderName: String? = null
)

@Serializable
data class BillingAddress(
    val firstName: String,
    val lastName: String,
    val address1: String,
    val address2: String? = null,
    val city: String,
    val state: String,
    val postalCode: String,
    val country: String
)

@Serializable
data class CustomerInfo(
    val email: String,
    val phoneNumber: String,
    val phone: String? = phoneNumber, // Alias for compatibility
    val customerId: String? = null,
    val ipAddress: String? = null
)


@Serializable
data class ReversalInformation(
    val paymentId: String,
    val amount: String,
    val amountDetails: AmountDetails? = null,
    val reason: String? = null
)

// ============================================
// API RESPONSE MODELS
// ============================================

@Serializable
data class PaymentResponse(
    val success: Boolean,
    val paymentId: String? = null,
    val status: PaymentStatus,
    val referenceCode: String? = null,
    val transactionDetails: TransactionDetails? = null,
    val approvalInfo: ApprovalInfo? = null,
    val error: ErrorInfo? = null,
    val metadata: PaymentMetadata? = null
)

@Serializable
enum class PaymentStatus {
    @SerialName("AUTHORIZED") AUTHORIZED,
    @SerialName("PENDING") PENDING,
    @SerialName("APPROVED") APPROVED,
    @SerialName("CAPTURED") CAPTURED,
    @SerialName("DECLINED") DECLINED,
    @SerialName("FAILED") FAILED,
    @SerialName("CANCELLED") CANCELLED,
    @SerialName("REFUNDED") REFUNDED,
    @SerialName("REVERSED") REVERSED,
    @SerialName("COMPLETED") COMPLETED,
    @SerialName("UNKNOWN") UNKNOWN
}

@Serializable
data class TransactionDetails(
    val amount: String? = null,
    val currency: String? = null,
    val approvalCode: String? = null,
    val transactionId: String? = null,
    val networkTransactionId: String? = null,
    val processorResponseCode: String? = null
)

@Serializable
data class ApprovalInfo(
    val canApprove: Boolean = false,
    val approvalAmount: String? = null,
    val approvalCode: String? = null,
    val authorizationCode: String? = null,
    val avsResult: String? = null,
    val cvvResult: String? = null
)

@Serializable
data class PaymentMetadata(
    val submitTime: String? = null,
    val processingTime: Long? = null,
    val riskScore: String? = null,
    val cardType: String? = null,
    val processorName: String? = null,
    val paymentMethodType: String? = null
)

@Serializable
data class ErrorInfo(
    val code: String,
    val message: String,
    val field: String? = null,
    val details: List<String>? = null
)

// ============================================
// CYBERSOURCE INTERNAL MODELS
// ============================================

@Serializable
data class CyberSourceAuthorizationRequest(
    val clientReferenceInformation: ClientReferenceInformation,
    val paymentInformation: PaymentInformation,
    val processingInformation: ProcessingInformation,
    val orderInformation: OrderInformation
)

@Serializable
data class CyberSourceCaptureRequest(
    val clientReferenceInformation: ClientReferenceInformation,
    val orderInformation: CaptureOrderInformation
)

@Serializable
data class ClientReferenceInformation(
    val code: String?
)

@Serializable
data class PaymentInformation(
    val card: Card
)

@Serializable
data class Card(
    val number: String,
    val expirationMonth: String,
    val expirationYear: String,
    val securityCode: String? = null,
    //val type: String? = null
)

@Serializable
data class OrderInformation(
    val amountDetails: AmountDetails,
    val billTo: BillTo
)

@Serializable
data class CaptureOrderInformation(
    val amountDetails: AmountDetails
)

@Serializable
data class AmountDetails(
    val totalAmount: String,
    val currency: String
)

@Serializable
data class BillTo(
    val firstName: String,
    val lastName: String,
    val address1: String,
    val locality: String,
    val administrativeArea: String,
    val postalCode: String,
    val country: String,
    val email: String,
    val phoneNumber: String
)

@Serializable
data class ProcessingInformation(
    val commerceIndicator: String
)

// ============================================
// CYBERSOURCE RESPONSE MODELS (UNCHANGED)
// ============================================

@Serializable
data class CyberSourcePaymentResponse(
    @SerialName("_links") val links: Map<String, Link>? = null,
    val id: String? = null,
    val submitTimeUtc: String? = null,
    val status: String? = null,
    val reconciliationId: String? = null,
    val errorInformation: ErrorInformation? = null,
    val clientReferenceInformation: ClientReferenceInformationResponse? = null,
    val processorInformation: ProcessorInformation? = null,
    val orderInformation: OrderInformationResponse? = null,
    val paymentInformation: PaymentInformationResponse? = null,
    val paymentAccountInformation: PaymentAccountInformation? = null,
    val pointOfSaleInformation: PointOfSaleInformation? = null
)

@Serializable
data class Link(
    val href: String? = null,
    val method: String? = null
)

@Serializable
data class ClientReferenceInformationResponse(
    val code: String? = null
)

@Serializable
data class ProcessorInformation(
    val approvalCode: String? = null,
    val responseCode: String? = null,
    val transactionId: String? = null,
    val networkTransactionId: String? = null,
    val avs: AVS? = null
)

@Serializable
data class AVS(
    val code: String? = null,
    val codeRaw: String? = null
)

@Serializable
data class OrderInformationResponse(
    val amountDetails: AmountDetailsResponse? = null
)

@Serializable
data class AmountDetailsResponse(
    val authorizedAmount: String? = null,
    val currency: String? = null
)

@Serializable
data class PaymentInformationResponse(
    val tokenizedCard: TokenizedCard? = null,
    val card: CardTypeOnly? = null
)

@Serializable
data class TokenizedCard(
    val type: String? = null
)

@Serializable
data class PaymentAccountInformation(
    val card: CardTypeOnly? = null
)

@Serializable
data class CardTypeOnly(
    val type: String? = null
)

@Serializable
data class PointOfSaleInformation(
    val terminalId: String? = null
)

@Serializable
data class ErrorInformation(
    val reason: String? = null,
    val message: String? = null,
    val details: List<ErrorDetail>? = null
)

@Serializable
data class ErrorDetail(
    val field: String? = null,
    val reason: String? = null
)