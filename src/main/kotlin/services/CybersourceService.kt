package services

import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.serialization.*
import kotlinx.serialization.json.*
import models.*
import org.slf4j.Logger
import utils.ConfigUtils
import java.security.MessageDigest
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import java.util.Base64

class CybersourceService(
    private val httpClient: HttpClient,
    private val logger: Logger
) {
    private val merchantKeyId = ConfigUtils.getString("cybersource.merchantKeyId").also {
        require(it.isNotBlank()) { "cybersource.merchantKeyId must be configured" }
    }

    private val merchantSecretKey = ConfigUtils.getString("cybersource.secretKey").also {
        require(it.isNotBlank()) { "cybersource.secretKey must be configured" }
    }

    private val merchantId = ConfigUtils.getString("cybersource.merchantId").also {
        require(it.isNotBlank()) { "cybersource.merchantId must be configured" }
    }

    private val host = ConfigUtils.getString("cybersource.host")
        .removePrefix("https://")
        .removePrefix("http://")
        .also {
            require(it.isNotBlank()) { "cybersource.host must be configured" }
        }

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        isLenient = true
        explicitNulls = false
    }

    init {
        logger.info("Initialized CyberSource Service with merchantId: $merchantId and host: $host")
    }

    suspend fun authorize(request: PaymentAuthorizationRequest): PaymentResponse {
        val resourcePath = "/pts/v2/payments"
        val url = "https://$host$resourcePath"

        return try {
            // Transform structured request to CyberSource format
            val cyberSourceRequest = transformToAuthorizationRequest(request)
            val requestBody = json.encodeToString(CyberSourceAuthorizationRequest.serializer(), cyberSourceRequest)

            logger.info("Sending authorization request to CyberSource for reference: ${request.orderDetails.referenceCode}")
            logger.info("Payment method: ${request.paymentMethod.type}, Platform: ${request.platform.source}")
            logger.debug("CyberSource request body: $requestBody")

            val response = makeSignedRequest(
                url = url,
                httpMethod = "POST",
                resourcePath = resourcePath,
                requestBody = requestBody
            )

            val cyberSourceResponse = handleCyberSourceResponse(response)
            transformAuthorizationResponse(cyberSourceResponse, request.orderDetails.referenceCode, request.paymentMethod.type)

        } catch (e: Exception) {
            logger.error("Authorization request failed for reference ${request.orderDetails.referenceCode}. Error: ${e.message}", e)
            PaymentResponse(
                success = false,
                status = PaymentStatus.FAILED,
                referenceCode = request.orderDetails.referenceCode,
                error = ErrorInfo(
                    code = "AUTHORIZATION_FAILED",
                    message = "Failed to process authorization: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }

    suspend fun approve(request: PaymentApprovalRequest): PaymentResponse {
        val resourcePath = "/pts/v2/payments/${request.paymentId}/captures"
        val url = "https://$host$resourcePath"

        return try {
            // Transform structured request to CyberSource format
            val cyberSourceRequest = transformToCaptureRequest(request)
            val requestBody = json.encodeToString(CyberSourceCaptureRequest.serializer(), cyberSourceRequest)

            logger.info("Sending approval (capture) request to CyberSource for payment ID: ${request.paymentId}")
            logger.debug("CyberSource approval request body: $requestBody")

            val response = makeSignedRequest(
                url = url,
                httpMethod = "POST",
                resourcePath = resourcePath,
                requestBody = requestBody
            )

            val cyberSourceResponse = handleCyberSourceResponse(response)
            transformApprovalResponse(cyberSourceResponse,request.paymentId)

        } catch (e: Exception) {
            logger.error("Approval request failed for payment ID ${request.paymentId}. Error: ${e.message}", e)
            PaymentResponse(
                success = false,
                status = PaymentStatus.FAILED,
                paymentId = request.paymentId,
                error = ErrorInfo(
                    code = "APPROVAL_FAILED",
                    message = "Failed to process approval: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }

    suspend fun reversePayment(reversalRequest: ReversalRequest): PaymentResponse {
        val paymentId = reversalRequest.reversalInformation.paymentId
        val resourcePath = "/pts/v2/payments/$paymentId/reversals"
        val url = "https://$host$resourcePath"

        return try {
            val requestBody = json.encodeToString(ReversalRequest.serializer(), reversalRequest)

            val response = makeSignedRequest(
                url = url,
                httpMethod = "POST",
                resourcePath = resourcePath,
                requestBody = requestBody
            )

            val cyberSourceResponse = handleCyberSourceResponse(response)
            transformStatusResponse(cyberSourceResponse, paymentId)

        } catch (e: Exception) {
            logger.error("Reversal failed for payment ID $paymentId. Error: ${e.message}", e)
            PaymentResponse(
                success = false,
                status = PaymentStatus.FAILED,
                paymentId = paymentId,
                error = ErrorInfo(
                    code = "REVERSAL_FAILED",
                    message = "Failed to reverse payment: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }


    private fun transformToAuthorizationRequest(request: PaymentAuthorizationRequest): CyberSourceAuthorizationRequest {
        return CyberSourceAuthorizationRequest(
            clientReferenceInformation = ClientReferenceInformation(
                code = request.orderDetails.referenceCode
            ),
            paymentInformation = PaymentInformation(
                card = Card(
                    number = request.cardDetails.number,
                    expirationMonth = request.cardDetails.expirationMonth,
                    expirationYear = request.cardDetails.expirationYear,
                    securityCode = request.cardDetails.securityCode,
                    //  type = request.cardDetails.type
                )
            ),
            orderInformation = OrderInformation(
                amountDetails = AmountDetails(
                    totalAmount = request.orderDetails.amount,
                    currency = request.orderDetails.currency
                ),
                billTo = BillTo(
                    firstName = request.billingAddress.firstName,
                    lastName = request.billingAddress.lastName,
                    address1 = request.billingAddress.address1,
                    locality = request.billingAddress.city,
                    administrativeArea = request.billingAddress.state,
                    postalCode = request.billingAddress.postalCode,
                    country = request.billingAddress.country,
                    email = request.customerInfo.email,
                    phoneNumber = request.customerInfo.phoneNumber
                )
            ),
            processingInformation = ProcessingInformation(
                commerceIndicator = when (request.platform.source) {
                    "web" -> "internet"
                    "mobile-app" -> "moto"
                    "api" -> "internet"
                    else -> "internet"
                }
            )
        )
    }

    private fun transformToCaptureRequest(request: PaymentApprovalRequest): CyberSourceCaptureRequest {
        return CyberSourceCaptureRequest(
            clientReferenceInformation = ClientReferenceInformation(
                code = request.referenceCode ?: "APPROVE_${request.paymentId}"
            ),
            orderInformation = CaptureOrderInformation(
                amountDetails = AmountDetails(
                    totalAmount = request.orderDetails.amount,
                    currency = request.orderDetails.currency
                )
            )
        )
    }

    private fun transformAuthorizationResponse(
        cyberSourceResponse: CyberSourcePaymentResponse,
        referenceCode: String?,
        paymentMethodType: String
    ): PaymentResponse {
        val isSuccessful = cyberSourceResponse.status == "AUTHORIZED" || cyberSourceResponse.status == "PENDING"
        val approvalUrl = cyberSourceResponse.links?.get("capture")?.href

        logger.info("Transforming CyberSource response: status=${cyberSourceResponse.status}, id=${cyberSourceResponse.id}")
        if (cyberSourceResponse.errorInformation != null) {
            logger.error("CyberSource error details: ${cyberSourceResponse.errorInformation.reason} - ${cyberSourceResponse.errorInformation.message}")
        }

        // Extract card type from response
        val cardType = cyberSourceResponse.paymentInformation?.card?.type
            ?: cyberSourceResponse.paymentAccountInformation?.card?.type


        return PaymentResponse(
            success = isSuccessful,
            paymentId = cyberSourceResponse.id,
            status = mapCyberSourceStatus(cyberSourceResponse.status),
            referenceCode = referenceCode,
            transactionDetails = TransactionDetails(
                amount = cyberSourceResponse.orderInformation?.amountDetails?.authorizedAmount,
                currency = cyberSourceResponse.orderInformation?.amountDetails?.currency,
                approvalCode = cyberSourceResponse.processorInformation?.approvalCode,
                transactionId = cyberSourceResponse.processorInformation?.transactionId,
                // networkTransactionId = cyberSourceResponse.processorInformation?.networkTransactionId,
                processorResponseCode = cyberSourceResponse.processorInformation?.responseCode
            ),
            approvalInfo = ApprovalInfo(
                canApprove = isSuccessful && approvalUrl != null,
                approvalAmount = cyberSourceResponse.orderInformation?.amountDetails?.authorizedAmount
            ),
            metadata = PaymentMetadata(
                submitTime = cyberSourceResponse.submitTimeUtc,
                cardType = cardType
            ),
            error = if (cyberSourceResponse.errorInformation != null) {
                ErrorInfo(
                    code = cyberSourceResponse.errorInformation.reason ?: "UNKNOWN_ERROR",
                    message = cyberSourceResponse.errorInformation.message ?: "Unknown error occurred",
                    details = cyberSourceResponse.errorInformation.details?.map { it.reason ?: "Unknown detail" }
                )
            } else if (!isSuccessful) {
                ErrorInfo(
                    code = "AUTHORIZATION_FAILED",
                    message = "Authorization was not successful. Status: ${cyberSourceResponse.status}"
                )
            } else null
        )
    }

    private fun transformApprovalResponse(
        cyberSourceResponse: CyberSourcePaymentResponse,
        referenceCode: String
    ): PaymentResponse {
        val isSuccessful = cyberSourceResponse.status == "PENDING" || cyberSourceResponse.status == "CAPTURED"

        return PaymentResponse(
            success = isSuccessful,
            paymentId = cyberSourceResponse.id,
            status = mapCyberSourceStatus(cyberSourceResponse.status),
            referenceCode = referenceCode,
            transactionDetails = TransactionDetails(
                amount = cyberSourceResponse.orderInformation?.amountDetails?.authorizedAmount,
                currency = cyberSourceResponse.orderInformation?.amountDetails?.currency,
                approvalCode = cyberSourceResponse.processorInformation?.approvalCode,
                transactionId = cyberSourceResponse.processorInformation?.transactionId,
                networkTransactionId = cyberSourceResponse.processorInformation?.networkTransactionId,
                processorResponseCode = cyberSourceResponse.processorInformation?.responseCode
            ),
            metadata = PaymentMetadata(
                submitTime = cyberSourceResponse.submitTimeUtc
            ),
            error = if (!isSuccessful && cyberSourceResponse.errorInformation != null) {
                ErrorInfo(
                    code = cyberSourceResponse.errorInformation.reason ?: "UNKNOWN_ERROR",
                    message = cyberSourceResponse.errorInformation.message ?: "Unknown error occurred",
                    details = cyberSourceResponse.errorInformation.details?.map { it.reason ?: "Unknown detail" }
                )
            } else if (!isSuccessful) {
                ErrorInfo(
                    code = "APPROVAL_FAILED",
                    message = "Approval was not successful. Status: ${cyberSourceResponse.status}"
                )
            } else null
        )
    }

    private fun transformStatusResponse(
        cyberSourceResponse: CyberSourcePaymentResponse,
        paymentId: String
    ): PaymentResponse {
        val isSuccessful = cyberSourceResponse.status != null

        return PaymentResponse(
            success = isSuccessful,
            paymentId = cyberSourceResponse.id ?: paymentId,
            status = mapCyberSourceStatus(cyberSourceResponse.status),
            referenceCode = cyberSourceResponse.clientReferenceInformation?.code,
            transactionDetails = TransactionDetails(
                amount = cyberSourceResponse.orderInformation?.amountDetails?.authorizedAmount,
                currency = cyberSourceResponse.orderInformation?.amountDetails?.currency,
                approvalCode = cyberSourceResponse.processorInformation?.approvalCode,
                transactionId = cyberSourceResponse.processorInformation?.transactionId,
                networkTransactionId = cyberSourceResponse.processorInformation?.networkTransactionId,
                processorResponseCode = cyberSourceResponse.processorInformation?.responseCode
            ),
            metadata = PaymentMetadata(
                submitTime = cyberSourceResponse.submitTimeUtc,
                cardType = cyberSourceResponse.paymentInformation?.card?.type
                    ?: cyberSourceResponse.paymentAccountInformation?.card?.type
            ),
            error = if (cyberSourceResponse.errorInformation != null) {
                ErrorInfo(
                    code = cyberSourceResponse.errorInformation.reason ?: "UNKNOWN_ERROR",
                    message = cyberSourceResponse.errorInformation.message ?: "Unknown error occurred",
                    details = cyberSourceResponse.errorInformation.details?.map { it.reason ?: "Unknown detail" }
                )
            } else null
        )
    }

    private fun mapCyberSourceStatus(status: String?): PaymentStatus {
        return when (status?.uppercase()) {
            "AUTHORIZED" -> PaymentStatus.AUTHORIZED
            "PENDING" -> PaymentStatus.PENDING
            "CAPTURED" -> PaymentStatus.APPROVED
            "DECLINED" -> PaymentStatus.DECLINED
            "CANCELLED" -> PaymentStatus.CANCELLED
            "REFUNDED" -> PaymentStatus.REFUNDED
            else -> PaymentStatus.FAILED
        }
    }



    private suspend fun makeSignedRequest(
        url: String,
        httpMethod: String,
        resourcePath: String,
        requestBody: String
    ): HttpResponse {
        val gmtDateTime = getGMTDateTime()
        val digest = if (httpMethod != "GET") getDigest(requestBody) else ""
        val signatureHeaderValue = getSignatureHeader(httpMethod, resourcePath, digest, gmtDateTime)

        logger.debug("""
            |--- CyberSource Request Details ---
            |URL: $url
            |Method: $httpMethod
            |Resource Path: $resourcePath
            |Headers:
            |  v-c-merchant-id: $merchantId
            |  Date: $gmtDateTime
            |  Host: $host
            |  Signature: $signatureHeaderValue
            |  Digest: SHA-256=$digest
            |Request Body: $requestBody
        """.trimMargin())

        return try {
            httpClient.request(url) {
                method = HttpMethod.parse(httpMethod)
                headers {
                    append("v-c-merchant-id", merchantId)
                    append("v-c-correlation-id", UUID.randomUUID().toString())
                    append("Date", gmtDateTime)
                    append("Host", host)
                    append("Signature", signatureHeaderValue)
                    if (httpMethod != "GET") {
                        append("Digest", "SHA-256=$digest")
                    }
                    append("Accept", "application/hal+json;charset=utf-8")
                    append("User-Agent", "Kotlin-CyberSource-Client/1.0")
                    if (httpMethod != "GET") {
                        contentType(ContentType.Application.Json)
                    }
                }
                if (httpMethod != "GET") {
                    setBody(requestBody)
                }
            }
        } catch (e: Exception) {
            logger.error("Failed to execute CyberSource request. Error: ${e.message}")
            throw e
        }
    }

    private fun getDigest(payload: String): String {
        return try {
            val digest = MessageDigest.getInstance("SHA-256").digest(payload.toByteArray(Charsets.UTF_8))
            Base64.getEncoder().encodeToString(digest)
        } catch (e: Exception) {
            logger.error("Failed to generate digest. Error: ${e.message}")
            throw e
        }
    }

    private fun getSignatureHeader(
        httpMethod: String,
        resourcePath: String,
        digest: String,
        gmtDateTime: String
    ): String {
        val signatureParam = getSignatureParam(httpMethod, resourcePath, digest, gmtDateTime)

        return buildString {
            append("keyid=\"$merchantKeyId\"")
            append(", algorithm=\"HmacSHA256\"")

            if (httpMethod.equals("GET", ignoreCase = true)) {
                append(", headers=\"host date request-target v-c-merchant-id\"")
            } else {
                append(", headers=\"host date request-target digest v-c-merchant-id\"")
            }

            append(", signature=\"$signatureParam\"")
        }
    }

    private fun getSignatureParam(
        httpMethod: String,
        resourcePath: String,
        digest: String,
        gmtDateTime: String
    ): String {
        val signatureString = buildString {
            append("host: $host")
            append("\n")
            append("date: $gmtDateTime")
            append("\n")
            append("request-target: ${httpMethod.lowercase()} $resourcePath")

            if (!httpMethod.equals("GET", ignoreCase = true)) {
                append("\n")
                append("digest: SHA-256=$digest")
            }

            append("\n")
            append("v-c-merchant-id: $merchantId")
        }

        logger.debug("Signature String to be signed:\n$signatureString")

        return try {
            val secretKey = SecretKeySpec(
                Base64.getDecoder().decode(merchantSecretKey),
                "HmacSHA256"
            )

            val mac = Mac.getInstance("HmacSHA256").apply {
                init(secretKey)
            }

            val signatureBytes = mac.doFinal(signatureString.toByteArray(Charsets.UTF_8))
            Base64.getEncoder().encodeToString(signatureBytes)
        } catch (e: Exception) {
            logger.error("Failed to generate signature. Error: ${e.message}")
            throw e
        }
    }

    private fun getGMTDateTime(): String {
        return DateTimeFormatter.RFC_1123_DATE_TIME.format(
            ZonedDateTime.now(ZoneId.of("GMT"))
        )
    }

    private suspend fun handleCyberSourceResponse(response: HttpResponse): CyberSourcePaymentResponse {
        val responseBody = response.bodyAsText()
        val statusCode = response.status

        logger.info("CyberSource response status: $statusCode")
        logger.info("CyberSource response headers: ${response.headers}")
        logger.debug("CyberSource response body: $responseBody")

        // Handle non-JSON responses (like "Resource not found")
        if (!statusCode.isSuccess() || responseBody.isBlank() || !responseBody.trim().startsWith("{")) {
            logger.error("Received non-JSON response from CyberSource. Status: $statusCode, Body: $responseBody")

            return CyberSourcePaymentResponse(
                status = "DECLINED",
                errorInformation = ErrorInformation(
                    reason = "API_ERROR",
                    message = "CyberSource API error (Status: $statusCode): $responseBody"
                )
            )
        }

        return try {
            json.decodeFromString(CyberSourcePaymentResponse.serializer(), responseBody).also {
                logger.info("Successfully parsed CyberSource response. Status: ${it.status}")
                if (it.errorInformation != null) {
                    logger.error("CyberSource returned error: ${it.errorInformation.reason} - ${it.errorInformation.message}")
                }
            }
        } catch (e: SerializationException) {
            logger.error("Failed to parse CyberSource response. Response body: $responseBody. Error: ${e.message}")

            CyberSourcePaymentResponse(
                status = "DECLINED",
                errorInformation = ErrorInformation(
                    reason = "RESPONSE_PARSING_ERROR",
                    message = "Failed to parse response: ${e.message}. Raw response: $responseBody"
                )
            )
        }
    }
}