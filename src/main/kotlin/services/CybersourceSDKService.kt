package services

import Api.PaymentsApi
import Api.CaptureApi
import Api.ReversalApi
import Invokers.ApiException
import Model.*
import models.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * Cybersource service implementation using the official REST SDK
 * Maintains the same API interface as the original CybersourceService
 */
class CybersourceSDKService {
    private val logger: Logger = LoggerFactory.getLogger(CybersourceSDKService::class.java)
    private val sdkConfig = CybersourceSDKConfig.getInstance()
    
    /**
     * Authorize a payment using Cybersource REST SDK
     */
    suspend fun authorize(request: PaymentAuthorizationRequest): PaymentResponse {
        return try {
            logger.info("Processing authorization request for reference: ${request.orderDetails.referenceCode}")
            logger.info("Payment method: ${request.paymentMethod.type}, Platform: ${request.platform.source}")
            
            // Create API client
            val apiClient = sdkConfig.createApiClient()
            val paymentsApi = PaymentsApi(apiClient)
            
            // Transform request to SDK format
            val sdkRequest = transformToSDKAuthorizationRequest(request)
            
            // Make the API call
            val response = paymentsApi.createPayment(sdkRequest)
            
            // Transform response back to our format
            transformSDKAuthorizationResponse(response, request.orderDetails.referenceCode ?: "", request.paymentMethod.type)
            
        } catch (e: ApiException) {
            logger.error("Authorization API exception for reference ${request.orderDetails.referenceCode}. Code: ${e.code}, Message: ${e.message}", e)
            PaymentResponse(
                success = false,
                status = PaymentStatus.FAILED,
                referenceCode = request.orderDetails.referenceCode,
                error = ErrorInfo(
                    code = "AUTHORIZATION_API_ERROR",
                    message = "API Error ${e.code}: ${e.message}"
                )
            )
        } catch (e: Exception) {
            logger.error("Authorization request failed for reference ${request.orderDetails.referenceCode}. Error: ${e.message}", e)
            PaymentResponse(
                success = false,
                status = PaymentStatus.FAILED,
                referenceCode = request.orderDetails.referenceCode,
                error = ErrorInfo(
                    code = "AUTHORIZATION_FAILED",
                    message = "Failed to process authorization: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }
    
    /**
     * Approve/Capture a payment using Cybersource REST SDK
     */
    suspend fun approve(request: PaymentApprovalRequest): PaymentResponse {
        return try {
            logger.info("Processing approval (capture) request for payment ID: ${request.paymentId}")
            
            // Create API client
            val apiClient = sdkConfig.createApiClient()
            val captureApi = CaptureApi(apiClient)
            
            // Transform request to SDK format
            val sdkRequest = transformToSDKCaptureRequest(request)
            
            // Make the API call
            val response = captureApi.capturePayment(sdkRequest, request.paymentId)
            
            // Transform response back to our format
            transformSDKCaptureResponse(response, request.paymentId)
            
        } catch (e: ApiException) {
            logger.error("Capture API exception for payment ID ${request.paymentId}. Code: ${e.code}, Message: ${e.message}", e)
            PaymentResponse(
                success = false,
                status = PaymentStatus.FAILED,
                paymentId = request.paymentId,
                error = ErrorInfo(
                    code = "CAPTURE_API_ERROR",
                    message = "API Error ${e.code}: ${e.message}"
                )
            )
        } catch (e: Exception) {
            logger.error("Capture request failed for payment ID ${request.paymentId}. Error: ${e.message}", e)
            PaymentResponse(
                success = false,
                status = PaymentStatus.FAILED,
                paymentId = request.paymentId,
                error = ErrorInfo(
                    code = "CAPTURE_FAILED",
                    message = "Failed to process capture: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }
    
    /**
     * Reverse a payment using Cybersource REST SDK
     */
    suspend fun reversePayment(reversalRequest: ReversalRequest): PaymentResponse {
        val paymentId = reversalRequest.reversalInformation.paymentId
        
        return try {
            logger.info("Processing reversal request for payment ID: $paymentId")
            
            // Create API client
            val apiClient = sdkConfig.createApiClient()
            val reversalApi = ReversalApi(apiClient)
            
            // Transform request to SDK format
            val sdkRequest = transformToSDKReversalRequest(reversalRequest)
            
            // Make the API call
            val response = reversalApi.authReversal(paymentId, sdkRequest)
            
            // Transform response back to our format
            transformSDKReversalResponse(response, paymentId)
            
        } catch (e: ApiException) {
            logger.error("Reversal API exception for payment ID $paymentId. Code: ${e.code}, Message: ${e.message}", e)
            PaymentResponse(
                success = false,
                status = PaymentStatus.FAILED,
                paymentId = paymentId,
                error = ErrorInfo(
                    code = "REVERSAL_API_ERROR",
                    message = "API Error ${e.code}: ${e.message}"
                )
            )
        } catch (e: Exception) {
            logger.error("Reversal request failed for payment ID $paymentId. Error: ${e.message}", e)
            PaymentResponse(
                success = false,
                status = PaymentStatus.FAILED,
                paymentId = paymentId,
                error = ErrorInfo(
                    code = "REVERSAL_FAILED",
                    message = "Failed to process reversal: ${e.message ?: "Unknown error"}"
                )
            )
        }
    }
    
    /**
     * Transform our authorization request to SDK format
     */
    private fun transformToSDKAuthorizationRequest(request: PaymentAuthorizationRequest): CreatePaymentRequest {
        val sdkRequest = CreatePaymentRequest()
        
        // Client reference information
        val clientRefInfo = Ptsv2paymentsClientReferenceInformation()
        clientRefInfo.code = request.orderDetails.referenceCode
        sdkRequest.clientReferenceInformation = clientRefInfo
        
        // Payment information
        val paymentInfo = Ptsv2paymentsPaymentInformation()
        val card = Ptsv2paymentsPaymentInformationCard()
        card.number = request.cardDetails.number
        card.expirationMonth = request.cardDetails.expirationMonth
        card.expirationYear = request.cardDetails.expirationYear
        if (!request.cardDetails.securityCode.isNullOrBlank()) {
            card.securityCode = request.cardDetails.securityCode
        }
        paymentInfo.card = card
        sdkRequest.paymentInformation = paymentInfo
        
        // Order information
        val orderInfo = Ptsv2paymentsOrderInformation()
        
        // Amount details
        val amountDetails = Ptsv2paymentsOrderInformationAmountDetails()
        amountDetails.totalAmount = request.orderDetails.amount
        amountDetails.currency = request.orderDetails.currency
        orderInfo.amountDetails = amountDetails
        
        // Billing information
        val billTo = Ptsv2paymentsOrderInformationBillTo()
        billTo.firstName = request.billingAddress.firstName
        billTo.lastName = request.billingAddress.lastName
        billTo.address1 = request.billingAddress.address1
        billTo.locality = request.billingAddress.city
        billTo.administrativeArea = request.billingAddress.state
        billTo.postalCode = request.billingAddress.postalCode
        billTo.country = request.billingAddress.country
        billTo.email = request.customerInfo.email
        billTo.phoneNumber = request.customerInfo.phoneNumber
        orderInfo.billTo = billTo
        
        sdkRequest.orderInformation = orderInfo
        
        return sdkRequest
    }

    /**
     * Transform our capture request to SDK format
     */
    private fun transformToSDKCaptureRequest(request: PaymentApprovalRequest): CapturePaymentRequest {
        val sdkRequest = CapturePaymentRequest()

        // Client reference information
        val clientRefInfo = Ptsv2paymentsClientReferenceInformation()
        clientRefInfo.code = request.referenceCode ?: "CAPTURE_${request.paymentId}"
        sdkRequest.clientReferenceInformation = clientRefInfo

        // Order information
        val orderInfo = Ptsv2paymentsidcapturesOrderInformation()
        val amountDetails = Ptsv2paymentsidcapturesOrderInformationAmountDetails()
        amountDetails.totalAmount = request.orderDetails.amount
        amountDetails.currency = request.orderDetails.currency
        orderInfo.amountDetails = amountDetails
        sdkRequest.orderInformation = orderInfo

        return sdkRequest
    }

    /**
     * Transform our reversal request to SDK format
     */
    private fun transformToSDKReversalRequest(request: ReversalRequest): AuthReversalRequest {
        val sdkRequest = AuthReversalRequest()

        // Client reference information
        val clientRefInfo = Ptsv2paymentsidreversalsClientReferenceInformation()
        clientRefInfo.code = "REVERSAL_${request.reversalInformation.paymentId}"
        sdkRequest.clientReferenceInformation = clientRefInfo

        // Reversal information
        val reversalInfo = Ptsv2paymentsidreversalsReversalInformation()
        val amountDetails = Ptsv2paymentsidreversalsReversalInformationAmountDetails()
        amountDetails.totalAmount = request.reversalInformation.amount
        reversalInfo.amountDetails = amountDetails
        reversalInfo.reason = request.reversalInformation.reason ?: "Customer requested reversal"
        sdkRequest.reversalInformation = reversalInfo

        return sdkRequest
    }

    /**
     * Transform SDK authorization response to our format
     */
    private fun transformSDKAuthorizationResponse(
        response: PtsV2PaymentsPost201Response,
        referenceCode: String,
        paymentMethodType: String
    ): PaymentResponse {
        val isSuccessful = response.status == "AUTHORIZED"

        return PaymentResponse(
            success = isSuccessful,
            paymentId = response.id,
            status = mapSDKStatus(response.status),
            referenceCode = referenceCode,
            transactionDetails = TransactionDetails(
                amount = response.orderInformation?.amountDetails?.authorizedAmount,
                currency = response.orderInformation?.amountDetails?.currency,
                approvalCode = response.processorInformation?.approvalCode,
                transactionId = response.processorInformation?.transactionId,
                networkTransactionId = response.processorInformation?.networkTransactionId,
                processorResponseCode = response.processorInformation?.responseCode
            ),
            approvalInfo = if (isSuccessful) {
                ApprovalInfo(
                    canApprove = true,
                    approvalCode = response.processorInformation?.approvalCode,
                    authorizationCode = response.processorInformation?.approvalCode
                )
            } else null,
            metadata = PaymentMetadata(
                submitTime = response.submitTimeUtc,
                paymentMethodType = paymentMethodType
            ),
            error = if (!isSuccessful) {
                ErrorInfo(
                    code = "AUTHORIZATION_DECLINED",
                    message = "Authorization was declined"
                )
            } else null
        )
    }

    /**
     * Transform SDK capture response to our format
     */
    private fun transformSDKCaptureResponse(
        response: PtsV2PaymentsCapturesPost201Response,
        paymentId: String
    ): PaymentResponse {
        val isSuccessful = response.status == "PENDING"

        return PaymentResponse(
            success = isSuccessful,
            paymentId = response.id ?: paymentId,
            status = mapSDKStatus(response.status),
            referenceCode = response.clientReferenceInformation?.code,
            transactionDetails = TransactionDetails(
                amount = response.orderInformation?.amountDetails?.totalAmount,
                currency = response.orderInformation?.amountDetails?.currency,
                transactionId = response.processorInformation?.transactionId
            ),
            metadata = PaymentMetadata(
                submitTime = response.submitTimeUtc
            ),
            error = if (!isSuccessful) {
                ErrorInfo(
                    code = "CAPTURE_FAILED",
                    message = "Capture failed"
                )
            } else null
        )
    }

    /**
     * Transform SDK reversal response to our format
     */
    private fun transformSDKReversalResponse(
        response: PtsV2PaymentsReversalsPost201Response,
        paymentId: String
    ): PaymentResponse {
        val isSuccessful = response.status == "REVERSED"

        return PaymentResponse(
            success = isSuccessful,
            paymentId = response.id ?: paymentId,
            status = mapSDKStatus(response.status),
            referenceCode = response.clientReferenceInformation?.code,
            transactionDetails = TransactionDetails(
                amount = response.reversalAmountDetails?.reversedAmount,
                transactionId = response.processorInformation?.transactionId
            ),
            metadata = PaymentMetadata(
                submitTime = response.submitTimeUtc
            ),
            error = if (!isSuccessful) {
                ErrorInfo(
                    code = "REVERSAL_FAILED",
                    message = "Reversal failed"
                )
            } else null
        )
    }

    /**
     * Map SDK status to our PaymentStatus enum
     */
    private fun mapSDKStatus(status: String?): PaymentStatus {
        return when (status?.uppercase()) {
            "AUTHORIZED" -> PaymentStatus.AUTHORIZED
            "PENDING" -> PaymentStatus.PENDING
            "REVERSED" -> PaymentStatus.REVERSED
            "DECLINED" -> PaymentStatus.DECLINED
            "FAILED" -> PaymentStatus.FAILED
            "CANCELLED" -> PaymentStatus.CANCELLED
            "COMPLETED" -> PaymentStatus.COMPLETED
            else -> PaymentStatus.UNKNOWN
        }
    }
}
