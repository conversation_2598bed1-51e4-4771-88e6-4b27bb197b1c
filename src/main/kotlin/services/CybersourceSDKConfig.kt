package services

import com.cybersource.authsdk.core.MerchantConfig
import Invokers.ApiClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import utils.ConfigUtils
import java.util.*

/**
 * Configuration service for Cybersource REST SDK
 * Handles initialization and provides configured API client instances
 */
class CybersourceSDKConfig {
    private val logger: Logger = LoggerFactory.getLogger(CybersourceSDKConfig::class.java)
    
    private val merchantProperties: Properties by lazy {
        loadMerchantProperties()
    }
    
    private val merchantConfig: MerchantConfig by lazy {
        // Force set critical properties programmatically before creating MerchantConfig
        merchantProperties.setProperty("runEnvironment", "CyberSource.Environment.SANDBOX")
        merchantProperties.setProperty("authenticationType", "http_signature")
        merchantProperties.setProperty("requestHost", "apitest.cybersource.com")
        merchantProperties.setProperty("requestTarget", "apitest.cybersource.com")
        merchantProperties.setProperty("serverHost", "apitest.cybersource.com")
        merchantProperties.setProperty("host", "apitest.cybersource.com")
        merchantProperties.setProperty("requestUrl", "https://apitest.cybersource.com")
        merchantProperties.setProperty("serverUrl", "https://apitest.cybersource.com")
        merchantProperties.setProperty("sendToProduction", "false")

        logger.info("Final properties before MerchantConfig creation:")
        merchantProperties.forEach { key, value ->
            if (key.toString().contains("password", ignoreCase = true) || key.toString().contains("secret", ignoreCase = true)) {
                logger.info("  $key = [REDACTED]")
            } else {
                logger.info("  $key = $value")
            }
        }

        MerchantConfig(merchantProperties)
    }
    
    /**
     * Creates a configured ApiClient instance for Cybersource REST SDK
     */
    fun createApiClient(): ApiClient {
        return ApiClient().apply {
            merchantConfig = <EMAIL>
        }
    }
    
    /**
     * Loads merchant properties from configuration files
     * Combines cybs.properties with application.conf settings
     */
    private fun loadMerchantProperties(): Properties {
        val properties = Properties()
        
        try {
            // Load from cybs.properties file
            val cybsPropertiesStream = this::class.java.classLoader.getResourceAsStream("cybs.properties")
            if (cybsPropertiesStream != null) {
                properties.load(cybsPropertiesStream)
                logger.info("Loaded cybs.properties configuration")
            } else {
                logger.warn("cybs.properties file not found, using application.conf only")
            }
            
            // Override with application.conf values if they exist
            overrideWithApplicationConfig(properties)
            
            // Validate required properties
            validateRequiredProperties(properties)

            // Debug: Print all properties for troubleshooting
            logger.debug("All loaded properties:")
            properties.forEach { key, value ->
                if (key.toString().contains("password", ignoreCase = true) || key.toString().contains("secret", ignoreCase = true)) {
                    logger.debug("  $key = [REDACTED]")
                } else {
                    logger.debug("  $key = $value")
                }
            }

            logger.info("Cybersource SDK configuration loaded successfully")
            logger.info("Merchant ID: ${properties.getProperty("merchantID")}")
            logger.info("Request Host: ${properties.getProperty("requestHost")}")
            logger.info("Server Host: ${properties.getProperty("serverHost")}")
            logger.info("Host: ${properties.getProperty("host")}")
            logger.info("Environment: ${if (properties.getProperty("sendToProduction", "false").toBoolean()) "Production" else "Sandbox"}")
            
        } catch (e: Exception) {
            logger.error("Failed to load Cybersource configuration", e)
            throw IllegalStateException("Failed to initialize Cybersource SDK configuration", e)
        }
        
        return properties
    }
    
    /**
     * Override properties with values from application.conf
     */
    private fun overrideWithApplicationConfig(properties: Properties) {
        try {
            // Override merchant ID
            val merchantId = ConfigUtils.getString("cybersource.merchantId")
            if (merchantId.isNotBlank()) {
                properties.setProperty("merchantID", merchantId)
            }

            // Override key filename
            val keyFilename = ConfigUtils.getString("cybersource.keyFilename")
            if (keyFilename.isNotBlank()) {
                properties.setProperty("keyFilename", keyFilename)
            }

            // Override key password
            val keyPassword = ConfigUtils.getString("cybersource.keyPassword")
            if (keyPassword.isNotBlank()) {
                properties.setProperty("keyPassword", keyPassword)
                properties.setProperty("keystorePassword", keyPassword)
            }

            // Override merchant secret key and key ID from application.conf
            val merchantSecretKey = ConfigUtils.getString("cybersource.secretKey")
            if (merchantSecretKey.isNotBlank()) {
                properties.setProperty("merchantsecretKey", merchantSecretKey)
            }

            val merchantKeyId = ConfigUtils.getString("cybersource.merchantKeyId")
            if (merchantKeyId.isNotBlank()) {
                properties.setProperty("merchantKeyId", merchantKeyId)
            }

            // Set host based on environment
            val host = ConfigUtils.getString("cybersource.host")
            if (host.isNotBlank()) {
                val isProduction = host.contains("api.cybersource.com")
                properties.setProperty("sendToProduction", isProduction.toString())

                // Set request host (remove https:// prefix and set multiple formats)
                val cleanHost = host.replace("https://", "").replace("http://", "")
                properties.setProperty("requestHost", cleanHost)
                properties.setProperty("serverHost", cleanHost)
                properties.setProperty("host", cleanHost)
                properties.setProperty("requestUrl", host)
                properties.setProperty("serverUrl", host)

                // Set the required runEnvironment property (correct SDK format)
                if (isProduction) {
                    properties.setProperty("runEnvironment", "CyberSource.Environment.PRODUCTION")
                    properties.setProperty("requestTarget", "api.cybersource.com")
                    properties.setProperty("serverHost", "api.cybersource.com")
                    properties.setProperty("host", "api.cybersource.com")
                    properties.setProperty("requestUrl", "https://api.cybersource.com")
                    properties.setProperty("serverUrl", "https://api.cybersource.com")
                    logger.info("Configured for PRODUCTION environment")
                } else {
                    properties.setProperty("runEnvironment", "CyberSource.Environment.SANDBOX")
                    properties.setProperty("requestTarget", "apitest.cybersource.com")
                    properties.setProperty("serverHost", "apitest.cybersource.com")
                    properties.setProperty("host", "apitest.cybersource.com")
                    properties.setProperty("requestUrl", "https://apitest.cybersource.com")
                    properties.setProperty("serverUrl", "https://apitest.cybersource.com")
                    logger.info("Configured for SANDBOX environment")
                }
            }

            // Ensure runEnvironment is always set (fallback to sandbox)
            if (!properties.containsKey("runEnvironment")) {
                properties.setProperty("runEnvironment", "CyberSource.Environment.SANDBOX")
                properties.setProperty("sendToProduction", "false")
                properties.setProperty("requestHost", "apitest.cybersource.com")
                properties.setProperty("requestTarget", "apitest.cybersource.com")
                properties.setProperty("serverHost", "apitest.cybersource.com")
                properties.setProperty("host", "apitest.cybersource.com")
                properties.setProperty("requestUrl", "https://apitest.cybersource.com")
                properties.setProperty("serverUrl", "https://apitest.cybersource.com")
                logger.info("Defaulting to SANDBOX environment")
            }

            // Ensure authentication type is always set
            if (!properties.containsKey("authenticationType")) {
                properties.setProperty("authenticationType", "http_signature")
            }

        } catch (e: Exception) {
            logger.warn("Some application.conf properties not found, using cybs.properties defaults", e)
            // Ensure runEnvironment is set even if config loading fails
            if (!properties.containsKey("runEnvironment")) {
                properties.setProperty("runEnvironment", "CyberSource.Environment.SANDBOX")
                properties.setProperty("sendToProduction", "false")
                properties.setProperty("requestHost", "apitest.cybersource.com")
                properties.setProperty("requestTarget", "apitest.cybersource.com")
                properties.setProperty("serverHost", "apitest.cybersource.com")
                properties.setProperty("host", "apitest.cybersource.com")
                properties.setProperty("requestUrl", "https://apitest.cybersource.com")
                properties.setProperty("serverUrl", "https://apitest.cybersource.com")
            }
            // Ensure authentication type is set even if config loading fails
            if (!properties.containsKey("authenticationType")) {
                properties.setProperty("authenticationType", "http_signature")
            }
        }
    }
    
    /**
     * Validates that all required properties are present
     */
    private fun validateRequiredProperties(properties: Properties) {
        val requiredProperties = listOf(
            "merchantID",
            "keyFilename",
            "keyPassword",
            "keystorePassword",
            "runEnvironment",
            "authenticationType"
        )

        // Check if at least one host property is set
        val hostProperties = listOf("requestHost", "serverHost", "host")
        val hasHostProperty = hostProperties.any { properties.containsKey(it) && !properties.getProperty(it).isNullOrBlank() }
        
        val missingProperties = requiredProperties.filter {
            properties.getProperty(it).isNullOrBlank()
        }

        if (missingProperties.isNotEmpty()) {
            throw IllegalStateException(
                "Missing required Cybersource configuration properties: ${missingProperties.joinToString(", ")}"
            )
        }

        if (!hasHostProperty) {
            throw IllegalStateException(
                "Missing required host configuration. At least one of these properties must be set: ${hostProperties.joinToString(", ")}"
            )
        }
    }
    
    /**
     * Gets the merchant ID from configuration
     */
    fun getMerchantId(): String {
        return merchantProperties.getProperty("merchantID")
    }
    
    /**
     * Checks if the configuration is for production environment
     */
    fun isProduction(): Boolean {
        return merchantProperties.getProperty("sendToProduction", "false").toBoolean()
    }
    
    companion object {
        @Volatile
        private var INSTANCE: CybersourceSDKConfig? = null
        
        /**
         * Gets the singleton instance of CybersourceSDKConfig
         */
        fun getInstance(): CybersourceSDKConfig {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CybersourceSDKConfig().also { INSTANCE = it }
            }
        }
    }
}
