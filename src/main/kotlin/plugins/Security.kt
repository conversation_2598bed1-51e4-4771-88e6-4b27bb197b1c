package plugins

import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.UserIdPrincipal

fun Application.configureSecurity() {

    // here we load api users from application.conf
    val configUsers = environment.config.configList("api.users").associate {
        it.property("name").getString() to it.property("password").getString()
    }


    install(Authentication) {
        basic("auth-basic") {
            realm = "Access to the payment API"
            validate { credentials ->
                val expectedPassword = configUsers[credentials.name]
                if (expectedPassword == credentials.password) {
                    UserIdPrincipal(credentials.name)
                } else {
                    null
                }
            }
        }
    }
}
