ktor {
  deployment {
    port = 8080
  }
  application {
    modules = [ ApplicationKt.module ]
  }
}

api.users = [
  { name = "admin", password = "password123" },
  { name = "abdool", password = "securepass1" },
  { name = "colleague1", password = "colleaguePass" }
]

database {
  host = "localhost"
  port = "5432"
  name = "your_db_name"
  schema = "public"
  user = "your_db_user"
  password = "your_db_password"
}

cybersource {
  merchantId = "esd000000026463"
  merchantKeyId = "6286ec14-1f81-4546-be9a-32770d69daee"
  secretKey = "/VGxpBzlSy+zQLrHRw1LVJy77qJ9i1DUZ5C/mHRf738="
  host = "https://apitest.cybersource.com"
}