# CyberSource SDK Configuration
# This file contains the configuration for the CyberSource SDK

# Merchant Configuration
merchantID=esd000000026463
keyAlias=serialnumber=1755189230278055332140,cn=esd000000026463
keyPassword=4545@65Ch
keystorePassword=4545@65Ch

# Keys Directory and File (Using Windows path format)
keysDirectory=C:\\Users\\<USER>\\Desktop\\Esdiac\\purchase\\keys
keyFilename=esd000000026463.p12

# API Configuration
targetAPIVersion=1.129
sendToProduction=false
sendToAkamai=true
useHttpClient=true
useHttpClientWithConnectionPool=false
timeout=130000

# Run Environment (modern SDK format - uses direct hostname)
runEnvironment=apitest.cybersource.com


# Authentication Configuration (required by SDK)
authenticationType=http_signature
merchantsecretKey=/VGxpBzlSy+zQLrHRw1LVJy77qJ9i1DUZ5C/mHRf738=
merchantKeyId=6286ec14-1f81-4546-be9a-32770d69daee

# Request Host Configuration (modern SDK format)
requestHost=apitest.cybersource.com
requestTarget=apitest.cybersource.com

# Alternative host configuration (try both formats)
serverHost=apitest.cybersource.com
host=apitest.cybersource.com

# Additional SDK host properties
requestUrl=https://apitest.cybersource.com
serverUrl=https://apitest.cybersource.com

# KeyStore Configuration
keystoreFormat=PKCS12
sslKeystoreFormat=PKCS12

# Logging Configuration
logDirectory=logs
logFilename=cybs.log
logMaximumSize=10MB
enableLog=true
logLevel=DEBUG

# Connection Pool Settings (if useHttpClientWithConnectionPool=true)
maxConnections=20
defaultMaxConnectionsPerRoute=10
maxConnectionsPerRoute=10
connectionRequestTimeoutMs=5000
connectionTimeoutMs=10000
socketTimeoutMs=120000
evictThreadSleepTimeMs=30000
maxKeepAliveTimeMs=60000
staleConnectionCheckEnabled=true
validateAfterInactivityMs=2000
enabledShutdownHook=true

# Retry Configuration
allowRetry=true
numberOfRetries=3
retryInterval=1000

# Security Configuration
enableJdkcert=false
enableCacert=false
certificateCacheEnabled=true
merchantConfigCacheEnabled=false

# Custom HTTP Configuration
customHttpClassEnabled=false
customHttpClass=

# Message Level Encryption
useSignAndEncrypted=false
