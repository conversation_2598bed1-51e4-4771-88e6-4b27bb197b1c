ktor {
  deployment {
    port = 8080
  }
  application {
    modules = [ ApplicationKt.module ]
  }
}

api.users = [
  { name = "admin", password = "your_password_here" },
  { name = "user1", password = "your_password_here" }
]



cybersource {
  merchantId = "your_merchant_id_here"
  merchantKeyId = "your_merchant_key_id_here"
  secretKey = "your_secret_key_here"
  host = "https://apitest.cybersource.com"
}
