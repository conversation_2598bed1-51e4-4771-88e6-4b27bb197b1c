[versions]
logback = "1.5.11"
logback-encoder = "7.4"
exposed = "1.0.0-beta-3"
postgresql = "42.7.5"
hikariCP = "6.2.1"
jbcrypt = "0.4"
libphonenumber = "9.0.10"
libphonenumber-geocoder = "3.10"
libphonenumber-prefixmapper = "3.10"
libphonenumber-carrier = "2.10"
ktorm = "3.6.0"

[libraries]
hikari = { module = "com.zaxxer:HikariCP", version.ref = "hikariCP" }
postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresql" }
jbcrypt = { module = "org.mindrot:jbcrypt", version.ref = "jbcrypt" }
exposed-core = { module = "org.jetbrains.exposed:exposed-core", version.ref = "exposed" }
exposed-dao = { module = "org.jetbrains.exposed:exposed-dao", version.ref = "exposed" }
exposed-jdbc = { module = "org.jetbrains.exposed:exposed-jdbc", version.ref = "exposed" }

ktorm-core = { module = "org.ktorm:ktorm-core", version.ref = "ktorm" }
ktorm-support-postgresql = { module = "org.ktorm:ktorm-support-postgresql", version.ref = "ktorm" }

logback-core = { module = "ch.qos.logback:logback-core", version.ref = "logback" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logback" }
logstash-encoder = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logback-encoder" }

libphonenumber = { module = "com.googlecode.libphonenumber:libphonenumber", version.ref = "libphonenumber" }
libphonenumber-geocoder = { module = "com.googlecode.libphonenumber:geocoder", version.ref = "libphonenumber-geocoder" }
libphonenumber-prefixmapper = { module = "com.googlecode.libphonenumber:prefixmapper", version.ref = "libphonenumber-prefixmapper" }
libphonenumber-carrier = { module = "com.googlecode.libphonenumber:carrier", version.ref = "libphonenumber-carrier" }