# Payment API Documentation

A comprehensive payment processing API that supports credit card transactions with initialization, approval, and reversal capabilities.

## Base URL
```
http://127.0.0.1:8080/api/v1
```

## API Endpoints

### 1. Initialize Payment

Initialize a payment transaction with the specified payment method.

**Endpoint:** `POST /payments/init`

**Description:** Creates a new payment transaction and authorizes the payment method. Users must specify the payment method type (e.g., credit-card).

#### Request Body

```json
{
    "paymentMethod": {
        "type": "credit-card"
    },
    "platform": {
        "source": "web"
    },
    "orderDetails": {
        "referenceCode": "ORDER-2025-007",
        "amount": "300.00",
        "currency": "USD"
    },
    "cardDetails": {
        "number": "****************",
        "expirationMonth": "12",
        "expirationYear": "2025",
        "securityCode": "123"
    },
    "billingAddress": {
        "firstName": "<PERSON>",
        "lastName": "Doe",
        "address1": "123 Main Street",
        "city": "New York",
        "state": "NY",
        "postalCode": "10001",
        "country": "US"
    },
    "customerInfo": {
        "email": "<EMAIL>",
        "phoneNumber": "******-123-4567"
    }
}
```

#### Response

```json
{
    "success": true,
    "paymentId": "7544224898576367204805",
    "status": "AUTHORIZED",
    "referenceCode": "ORDER-2025-007",
    "transactionDetails": {
        "amount": "300.00",
        "currency": "USD",
        "approvalCode": "831000",
        "transactionId": "MCC053823",
        "processorResponseCode": "000"
    },
    "approvalInfo": {
        "canApprove": true,
        "approvalAmount": "300.00"
    },
    "metadata": {
        "submitTime": "2025-08-05T19:34:50Z",
        "cardType": "002"
    }
}
```

### 2. Approve Payment

Approve a previously authorized payment transaction.

**Endpoint:** `POST /payments/approve`

**Description:** Processes the final approval of an authorized payment transaction.

#### Request Body

```json
{
  "paymentId": "7546610062536917404806",
  "orderDetails": {
    "amount": "300.00",
    "currency": "USD"
  }
}
```

#### Response

```json
{
    "success": true,
    "paymentId": "7544225538586531004807",
    "status": "PENDING",
    "referenceCode": "7544224898576367204805",
    "transactionDetails": {
        "currency": "USD"
    },
    "metadata": {
        "submitTime": "2025-08-05T19:35:53Z"
    }
}
```

### 3. Reverse Payment

Reverse or refund a completed payment transaction.

**Endpoint:** `POST /payments/reverse`

**Description:** Initiates a reversal of a previously processed payment transaction.

#### Request Body

```json
{
  "reversalInformation": {
    "paymentId": "7546606788956173004807",
    "amountDetails": {
      "totalAmount": "300.00",
      "currency": "USD"
    },
    "reason": "testing"
  }
}
```

#### Response

```json
{
    "success": true,
    "paymentId": "7544219011206334104806",
    "status": "FAILED",
    "referenceCode": "ORDER-2025-004",
    "transactionDetails": {
        "currency": "USD",
        "processorResponseCode": "000"
    },
    "metadata": {
        "submitTime": "2025-08-05T19:25:01Z"
    }
}
```

## Payment Flow

1. **Initialize Payment** - Create and authorize a payment transaction
2. **Approve Payment** - Complete the payment processing
3. **Reverse Payment** - Refund or reverse a transaction (if needed)

## Payment Method Types

Currently supported payment methods:
- `credit-card` - Credit card payments

## Status Codes

- `AUTHORIZED` - Payment has been authorized but not yet captured
- `PENDING` - Payment is being processed
- `FAILED` - Payment processing failed
- `COMPLETED` - Payment successfully processed
- `REVERSED` - Payment has been reversed/refunded

## Request Fields

### Payment Method
- `type` (string, required) - The payment method type (e.g., "credit-card")

### Platform
- `source` (string) - The platform source (e.g., "web", "mobile")

### Order Details
- `referenceCode` (string, required) - Unique order reference
- `amount` (string, required) - Transaction amount
- `currency` (string, required) - Currency code (e.g., "USD")

### Card Details (for credit-card payments)
- `number` (string, required) - Credit card number
- `expirationMonth` (string, required) - Card expiration month
- `expirationYear` (string, required) - Card expiration year
- `securityCode` (string, required) - Card security code (CVV)

### Billing Address
- `firstName` (string, required) - Cardholder first name
- `lastName` (string, required) - Cardholder last name
- `address1` (string, required) - Primary address line
- `city` (string, required) - City
- `state` (string, required) - State/Province
- `postalCode` (string, required) - Postal/ZIP code
- `country` (string, required) - Country code

### Customer Information
- `email` (string, required) - Customer email address
- `phoneNumber` (string) - Customer phone number

## Error Handling

All API responses include a `success` boolean field. When `success` is `false`, additional error information will be provided in the response.

## Security Notes

- All API calls should be made over HTTPS in production
- Credit card numbers and sensitive data should be handled according to PCI DSS compliance standards
- Store payment IDs securely for transaction tracking and future operations

## Testing

Use the provided sample requests with test credit card numbers for development and testing purposes. The example uses a test Mastercard number (****************).